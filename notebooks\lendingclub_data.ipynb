import pandas as pd
import pathlib as pl

# Make sure jupyter lab's current directory is the project root
DATA_PATH = pl.Path.cwd() / "data" / "Lending Club loan data" / "loan.csv"


chunk_size = 10000
chunks = []

for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):
    chunks.append(chunk)

df = pd.concat(chunks, ignore_index=True)

column_indices = [19, 47, 123, 124, 125, 128, 129, 130, 133, 139, 140, 141]

# Iterate through the indices and print the column name and unique values
for idx in column_indices:
    if idx < len(df.columns):
        column_name = df.columns[idx]
        unique_values = df.iloc[:, idx].unique()
        print(f"Column {idx} - '{column_name}':")
        print(f"Unique values: {unique_values}")
        print("+" + "="*50 + "+")
    else:
        print(f"Column {idx}: Index out of range")

df.shape

# print(df["hardship_start_date"].unique())
# print(df["hardship_end_date"].unique())

columns = ["hardship_flag", "hardship_type", "hardship_reason", "hardship_status", "deferral_term", "hardship_amount", "hardship_start_date", "hardship_end_date", "payment_plan_start_date", "hardship_length", "hardship_dpd", "hardship_loan_status"]

for col in columns:
    unique_values = df[col].unique()
    print(f"Number of unique values: {len(unique_values)}") 
    print(f"Unique values in '{col}': {unique_values}")
    print('-'*50)
    print(f"Count of each unique value:")
    for unique_value in unique_values:
        count = (df[col] == unique_value).sum()
        print(f"{unique_value}: {count}")

    print("+" + "="*50 + "+")

df["loan_status"].unique()

# Let's first examine the problematic columns mentioned in the warning
# Columns (123,124,125,128,129,130,133,139,140,141) have mixed types

# First, let's read just the header to see column names
# header_df = pd.DataFrame(df.iloc[0, :])
header_df = pd.read_csv(DATA_PATH, nrows=0)
print(f"Total columns: {len(header_df.columns)}")
print("\nColumn names at problematic indices:")
problematic_indices = [123, 124, 125, 128, 129, 130, 133, 139, 140, 141]
for idx in problematic_indices:
    if idx < len(header_df.columns):
        print(f"Column {idx}: {header_df.columns[idx]}")
    else:
        print(f"Column {idx}: Index out of range")

# Now let's examine a small sample to see the mixed types
print("\n" + "="*50)
print("EXAMINING MIXED TYPE COLUMNS")
print("="*50)

# Read a small sample to inspect the data types
sample_df = pd.read_csv(DATA_PATH, nrows=1000, low_memory=False)
print(f"\nSample data shape: {sample_df.shape}")

# Check the problematic columns
for idx in problematic_indices:
    if idx < len(sample_df.columns):
        col_name = sample_df.columns[idx]
        print(f"\nColumn {idx} - '{col_name}':")
        print(f"  Data type: {sample_df[col_name].dtype}")
        print(f"  Unique values (first 10): {sample_df[col_name].unique()[:10]}")
        print(f"  Non-null count: {sample_df[col_name].count()}/{len(sample_df)}")

features = [
    'loan_amnt',
    'int_rate',
    'installment',
    'grade',
    'sub_grade',
    'annual_inc',
    'dti',
    'fico_range_low',
    'fico_range_high',
    'revol_util',
    'revol_bal',
    'emp_length',
    'home_ownership',
    'verification_status',
    'purpose',
    'term',
    'open_acc',
    'total_acc',
    'inq_last_6mths',
    'delinq_2yrs',
    'pub_rec',
    'pub_rec_bankruptcies',
    'mths_since_last_delinq',
    'acc_open_past_24mths',
    'mort_acc',
    'avg_cur_bal',
    'bc_util',
    'tot_hi_cred_lim',
    'mo_sin_old_rev_tl_op',
    'num_actv_rev_tl'
]

df["loan_status"].unique()

df["grade"].unique()

dict(df["grade"].value_counts())

df["home_ownership"].unique()

dict(df["home_ownership"].value_counts())

df["verification_status"].unique()

dict(df["verification_status"].value_counts())

df["purpose"].unique()

dict(df["purpose"].value_counts())

df["loan_status"].unique()

dict(df["loan_status"].value_counts())

# Unique values for LoanGrade
print("Unique values in 'grade':")
print(df["grade"].unique())

# Value counts for LoanGrade
print("Value counts for 'grade':")
dict(df["grade"].value_counts())

# Unique values for HomeOwnership
print("Unique values in 'home_ownership':")
print(df["home_ownership"].unique())

# Value counts for HomeOwnership
print("Value counts for 'home_ownership':")
dict(df["home_ownership"].value_counts())

# Unique values for VerificationStatus
print("Unique values in 'verification_status':")
print(df["verification_status"].unique())

# Value counts for VerificationStatus
print("Value counts for 'verification_status':")
dict(df["verification_status"].value_counts())

# Unique values for LoanPurpose
print("Unique values in 'purpose':")
print(df["purpose"].unique())

# Value counts for LoanPurpose
print("Value counts for 'purpose':")
dict(df["purpose"].value_counts())

# Unique values for LoanStatus
print("Unique values in 'loan_status':")
print(df["loan_status"].unique())

# Value counts for LoanStatus
print("Value counts for 'loan_status':")
dict(df["loan_status"].value_counts())